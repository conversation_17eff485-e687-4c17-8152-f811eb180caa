export interface Country {
  code: string;
  name: string;
  dialCode: string;
  flag: string;
  translationKey: string;
}

export const COUNTRIES: Country[] = [
  {
    code: 'SA',
    name: 'Saudi Arabia',
    dialCode: '+966',
    flag: '🇸🇦',
    translationKey: 'auth.countries.sa',
  },
  {
    code: 'AE',
    name: 'United Arab Emirates',
    dialCode: '+971',
    flag: '🇦🇪',
    translationKey: 'auth.countries.ae',
  },
  {
    code: 'KW',
    name: 'Kuwait',
    dialCode: '+965',
    flag: '🇰🇼',
    translationKey: 'auth.countries.kw',
  },
  {
    code: 'QA',
    name: 'Qatar',
    dialCode: '+974',
    flag: '🇶🇦',
    translationKey: 'auth.countries.qa',
  },
  {
    code: 'BH',
    name: 'Bahrain',
    dialCode: '+973',
    flag: '🇧🇭',
    translationKey: 'auth.countries.bh',
  },
];

export const DEFAULT_COUNTRY = COUNTRIES[0]; // Saudi Arabia as default
