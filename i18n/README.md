# Multi-Language Support (i18n) Setup

This project uses `i18next` and `react-i18next` for internationalization with support for English and Arabic languages.

## Features

- ✅ English and Arabic language support
- ✅ Automatic language detection and persistence
- ✅ RTL (Right-to-Left) layout support for Arabic
- ✅ Type-safe translation keys
- ✅ Easy language switching
- ✅ AsyncStorage integration for language persistence

## File Structure

```
i18n/
├── index.ts              # i18n configuration
├── locales/
│   ├── en.json          # English translations
│   └── ar.json          # Arabic translations
└── README.md            # This file

utils/
└── languageStorage.ts   # Language storage utilities

contexts/
└── LanguageContext.tsx  # Language context provider

hooks/
└── useLanguage.ts       # Custom hook for language management
```

## Usage

### 1. Basic Translation

```tsx
import { useLanguage } from '@/hooks/useLanguage';

const MyComponent = () => {
  const { t } = useLanguage();

  return (
    <Text>{t('common.loading')}</Text>
  );
};
```

### 2. Language Switching

```tsx
import { useLanguage } from '@/hooks/useLanguage';

const LanguageSwitcher = () => {
  const { changeLanguage, currentLanguageType } = useLanguage();

  const handleLanguageChange = async (language: 'english' | 'arabic') => {
    try {
      await changeLanguage(language);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  return (
    <View>
      <Button 
        title="English" 
        onPress={() => handleLanguageChange('english')}
        disabled={currentLanguageType === 'english'}
      />
      <Button 
        title="العربية" 
        onPress={() => handleLanguageChange('arabic')}
        disabled={currentLanguageType === 'arabic'}
      />
    </View>
  );
};
```

### 3. RTL Layout Support

```tsx
import { useLanguage } from '@/hooks/useLanguage';

const MyComponent = () => {
  const { isRTL } = useLanguage();

  return (
    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
      <Text>Content that respects RTL</Text>
    </View>
  );
};
```

### 4. Interpolation (Variables in Translations)

```tsx
// In translation files:
// en.json: { "welcome": "Welcome, {{name}}!" }
// ar.json: { "welcome": "مرحباً، {{name}}!" }

const { t } = useLanguage();
return <Text>{t('welcome', { name: 'John' })}</Text>;
```

## Adding New Translations

1. Add the key-value pair to both `en.json` and `ar.json`
2. Use nested objects for organization:

```json
{
  "screens": {
    "home": {
      "title": "Home",
      "subtitle": "Welcome to the app"
    }
  }
}
```

3. Access nested translations: `t('screens.home.title')`

## Language Codes

- English: `en`
- Arabic: `ar`

## Best Practices

1. **Organize translations by feature/screen**
2. **Use descriptive keys**: `button.save` instead of `btn1`
3. **Keep translations consistent** across languages
4. **Test RTL layout** when adding Arabic translations
5. **Use the `useLanguage` hook** instead of `useTranslation` directly
6. **Handle loading states** when language is being initialized

## Available Hooks and Utilities

### `useLanguage()`
- `t(key, options?)` - Translation function
- `currentLanguage` - Current language code ('en' | 'ar')
- `currentLanguageType` - Current language type ('english' | 'arabic')
- `isRTL` - Boolean indicating if current language is RTL
- `isLoading` - Boolean indicating if language is being loaded
- `changeLanguage(type)` - Function to change language

### Language Storage Utils
- `saveLanguage(type)` - Save language preference
- `getSavedLanguage()` - Get saved language
- `getCurrentLanguageType()` - Get current language type
- `isRTL()` - Check if current language is RTL

## Troubleshooting

1. **Translations not updating**: Make sure you're using the `useLanguage` hook
2. **RTL not working**: Check if `I18nManager.allowRTL(true)` is called
3. **Language not persisting**: Verify AsyncStorage permissions
4. **Missing translations**: Check console for missing key warnings
