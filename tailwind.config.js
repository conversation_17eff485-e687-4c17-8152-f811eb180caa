/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all files that contain Nativewind classes.
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      fontFamily: {
        primary: "Cairo-Regular",
        "cairo-black": "Cairo-Black",
        "cairo-bold": "Cairo-Bold",
        "cairo-extrabold": "Cairo-ExtraBold",
        "cairo-extralight": "Cairo-ExtraLight",
        "cairo-light": "Cairo-Light",
        "cairo-medium": "Cairo-Medium",
        "cairo-regular": "Cairo-Regular",
        "cairo-semibold": "Cairo-SemiBold",
        // Keep the original Cairo mapping for backward compatibility
        cairo: "Cairo-Regular",
      },
      colors: {
        primary: "#FFC700",
      },
    },
  },
  plugins: [],
}

