import { useTranslation } from 'react-i18next';
import { useLanguageContext } from '@/contexts/LanguageContext';
import { saveLanguage, type LanguageType, type LanguageCode } from '@/utils/languageStorage';

export const useLanguage = () => {
  const { t, i18n } = useTranslation();
  const { currentLanguage, isRTL, isLoading } = useLanguageContext();

  const changeLanguage = async (languageType: LanguageType) => {
    try {
      await saveLanguage(languageType);
    } catch (error) {
      console.error('Error changing language:', error);
      throw error;
    }
  };

  const getCurrentLanguageCode = (): LanguageCode => {
    return currentLanguage;
  };

  const getCurrentLanguageType = (): LanguageType => {
    return currentLanguage === 'ar' ? 'arabic' : 'english';
  };

  return {
    t,
    currentLanguage,
    currentLanguageType: getCurrentLanguageType(),
    isRTL,
    isLoading,
    changeLanguage,
    getCurrentLanguageCode,
    getCurrentLanguageType,
  };
};
