import React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { useLanguage } from '@/hooks/useLanguage';
import { type LanguageType } from '@/utils/languageStorage';

/**
 * Demo component showing how to use the translation system
 */
export const LanguageDemo: React.FC = () => {
  const { t, currentLanguageType, isRTL, changeLanguage, isLoading } = useLanguage();

  const handleLanguageChange = async (language: LanguageType) => {
    try {
      await changeLanguage(language);
      Alert.alert(
        t('common.ok'),
        `Language changed to ${language === 'english' ? 'English' : 'العربية'}`
      );
    } catch (error) {
      Alert.alert(t('common.error'), 'Failed to change language');
    }
  };

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg font-cairo-regular">{t('common.loading')}</Text>
      </View>
    );
  }

  return (
    <View className="p-6 bg-white">
      <Text className="text-2xl font-cairo-bold mb-4">
        Language Demo
      </Text>
      
      {/* Current Language Info */}
      <View className="mb-6 p-4 bg-gray-100 rounded-lg">
        <Text className="text-lg font-cairo-semibold mb-2">Current Language:</Text>
        <Text className="font-cairo-regular">Type: {currentLanguageType}</Text>
        <Text className="font-cairo-regular">RTL: {isRTL ? 'Yes' : 'No'}</Text>
      </View>

      {/* Translation Examples */}
      <View className="mb-6">
        <Text className="text-lg font-cairo-semibold mb-2">Translation Examples:</Text>
        <Text className="font-cairo-regular mb-1">• {t('common.loading')}</Text>
        <Text className="font-cairo-regular mb-1">• {t('common.error')}</Text>
        <Text className="font-cairo-regular mb-1">• {t('common.save')}</Text>
        <Text className="font-cairo-regular mb-1">• {t('common.cancel')}</Text>
      </View>

      {/* Language Switcher */}
      <View className="mb-6">
        <Text className="text-lg font-cairo-semibold mb-3">Switch Language:</Text>
        <View className="flex-row gap-3">
          <TouchableOpacity
            className={`px-4 py-2 rounded-lg ${
              currentLanguageType === 'english' ? 'bg-primary' : 'bg-gray-200'
            }`}
            onPress={() => handleLanguageChange('english')}
            disabled={currentLanguageType === 'english'}
          >
            <Text className={`font-cairo-medium ${
              currentLanguageType === 'english' ? 'text-black' : 'text-gray-600'
            }`}>
              English
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            className={`px-4 py-2 rounded-lg ${
              currentLanguageType === 'arabic' ? 'bg-primary' : 'bg-gray-200'
            }`}
            onPress={() => handleLanguageChange('arabic')}
            disabled={currentLanguageType === 'arabic'}
          >
            <Text className={`font-cairo-medium ${
              currentLanguageType === 'arabic' ? 'text-black' : 'text-gray-600'
            }`}>
              العربية
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* RTL Layout Demo */}
      <View className="mb-6">
        <Text className="text-lg font-cairo-semibold mb-3">RTL Layout Demo:</Text>
        <View 
          className="flex-row p-3 bg-blue-100 rounded-lg"
          style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
        >
          <View className="w-8 h-8 bg-blue-500 rounded mr-3" />
          <Text className="font-cairo-regular flex-1">
            This content respects RTL layout direction
          </Text>
        </View>
      </View>

      {/* Onboarding Translations Demo */}
      <View>
        <Text className="text-lg font-cairo-semibold mb-3">Onboarding Translations:</Text>
        <Text className="font-cairo-regular mb-2">• {t('onboarding.slide1.title')}</Text>
        <Text className="font-cairo-regular mb-2">• {t('onboarding.signIn')}</Text>
        <Text className="font-cairo-regular">• {t('chooseLanguage.title')}</Text>
      </View>
    </View>
  );
};
