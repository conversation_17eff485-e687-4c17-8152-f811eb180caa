import React, { useRef, useState } from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

const { width } = Dimensions.get('window');

interface SlideItem {
  id: string;
  image: any;
  title: string;
  description: string;
}

interface OnboardingSliderProps {
  slides: SlideItem[];
}

const OnboardingSlider: React.FC<OnboardingSliderProps> = ({ slides }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(contentOffsetX / width);
    setActiveIndex(currentIndex);
  };

  const goToSlide = (index: number) => {
    if (flatListRef.current) {
      flatListRef.current.scrollToIndex({
        index,
        animated: true,
      });
    }
  };

  const renderItem = ({ item }: { item: SlideItem }) => {
    return (
      <View className="w-full items-center justify-center px-6" style={{ width }}>
        <View className="items-center justify-center mb-8">
          <Image
            source={item.image}
            className="w-96 h-72"
            resizeMode="contain"
          />
        </View>
        <Text className="text-2xl text-center text-black font-cairo-bold mb-4 leading-8">
          {item.title}
        </Text>
        <Text className="text-base text-center text-gray-600 font-cairo-regular leading-6 px-4">
          {item.description}
        </Text>
      </View>
    );
  };

  return (
    <View className="flex-1">
      <FlatList
        ref={flatListRef}
        data={slides}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        keyExtractor={(item) => item.id}
        style={{ width }}
        snapToInterval={width}
        decelerationRate="fast"
      />
      
      <View className="flex-row justify-center items-center mt-6 mb-8">
        {slides.map((_, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => goToSlide(index)}
            className={`mx-1 ${
              index === activeIndex ? 'w-8 h-2 bg-primary' : 'w-2 h-2 bg-gray-300'
            } rounded-full`}
          />
        ))}
      </View>
    </View>
  );
};

export default OnboardingSlider;
