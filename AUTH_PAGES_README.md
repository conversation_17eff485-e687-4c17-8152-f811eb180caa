# Authentication Pages Documentation

## Overview
Perfect Sign In and Sign Up pages have been created with exact clean design matching the provided mockups. Both pages use Tailwind CSS for styling and follow the SAFQA Shipping design system.

## Features Implemented

### Sign In Page (`/signin`)
- **Clean Design**: Matches the provided mockup exactly
- **Logo**: SAFQA Shipping logo at the top
- **Welcome Text**: "Welcome Back" with subtitle
- **Form Fields**:
  - Email input (pre-filled with `<EMAIL>`)
  - Password input (pre-filled with `Test@1234`) with show/hide toggle
  - Remember me checkbox (checked by default)
  - Forget Password link
- **Sign In Button**: Primary yellow button
- **Divider**: "Or sign in with" section
- **Terms**: Terms & conditions and Privacy policy links
- **Navigation**: Link to Sign Up page
- **Bottom Indicator**: Black rounded indicator bar

### Sign Up Page (`/signup`)
- **Clean Design**: Matches the provided mockup exactly
- **Logo**: SAFQA Shipping logo at the top
- **Create Account Text**: "Create Account" with subtitle
- **User Type Selection**:
  - Car Owner (with car icon from `assets/images/auth/car-icon.png`)
  - Transporter (with truck icon from `assets/images/auth/truck-icon.png`)
- **Form Fields**:
  - First Name and Last Name (side by side)
  - Email ID
  - Mobile Number with Saudi Arabia country code (+966) and flag
  - Password with show/hide toggle
  - Confirm Password with show/hide toggle
- **Checkboxes**:
  - Accept terms & conditions
  - Agree to receive notification emails
- **Sign Up Button**: Primary yellow button
- **Navigation**: Link to Sign In page
- **Bottom Indicator**: Black rounded indicator bar

## Styling Details

### Colors
- **Primary**: `#FFC700` (Yellow/Gold) - defined in `tailwind.config.js`
- **Background**: White
- **Text**: Various shades of gray and black
- **Input Background**: Light gray (`bg-gray-50`)

### Typography
- **Font Family**: Cairo (all variants available)
- **Font Weights**: Regular, Medium, SemiBold, Bold
- **Responsive**: Proper font sizes for mobile

### Layout
- **Responsive**: Works on all screen sizes
- **Keyboard Avoiding**: Proper keyboard handling
- **Scroll Support**: ScrollView for smaller screens
- **Safe Area**: Proper safe area handling

## Navigation

### From Onboarding
- **Sign In Button**: Navigates to `/signin`
- **Join Now Button**: Navigates to `/signup`

### Between Auth Pages
- **Sign In → Sign Up**: "Join SAFQA Shipping" link
- **Sign Up → Sign In**: "Sign in" link

### After Authentication
- Both pages navigate to `/(tabs)` on successful authentication

## Testing

### Quick Testing
To test the auth pages directly, you can temporarily modify `app/index.tsx`:

```typescript
// For testing Sign In page
return <Redirect href="/signin" />;

// For testing Sign Up page
return <Redirect href="/signup" />;
```

### Form Validation
- All required fields are marked with asterisks (*)
- Password visibility toggle works
- Checkbox states are properly managed
- Form submission logs data to console

## Assets Used
- **Logo**: `assets/images/logo.png`
- **Car Icon**: `assets/images/auth/car-icon.png`
- **Transporter Icon**: `assets/images/auth/truck-icon.png`
- **Saudi Flag**: Unicode emoji 🇸🇦

## Dependencies
- **React Native**: Core components
- **Expo Router**: Navigation
- **Ionicons**: Icons for password toggle, checkmarks, etc.
- **Tailwind CSS**: Styling via NativeWind
- **Cairo Fonts**: Typography

## Implementation Notes
- Uses functional components with React hooks
- State management for form fields and UI states
- Proper TypeScript typing
- Responsive design principles
- Accessibility considerations
- Clean, maintainable code structure

## Next Steps
1. Integrate with authentication API
2. Add form validation
3. Add loading states
4. Add error handling
5. Add biometric authentication options
6. Add social login options
