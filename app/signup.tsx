import { Ionicons } from '@expo/vector-icons';
import { Link, router } from 'expo-router';
import React, { useState } from 'react';
import {
    Image,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    ScrollView,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

export default function SignUp() {
  const [userType, setUserType] = useState<'car-owner' | 'transporter'>('car-owner');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [countryCode, setCountryCode] = useState('+966');
  const [mobileNumber, setMobileNumber] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [acceptNotifications, setAcceptNotifications] = useState(false);

  const handleSignUp = () => {
    // Handle sign up logic here
    console.log('Sign up with:', {
      userType,
      firstName,
      lastName,
      email,
      countryCode,
      mobileNumber,
      password,
      confirmPassword,
      acceptTerms,
      acceptNotifications,
    });
    // Navigate to main app or verification screen
    router.replace('/(tabs)');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-1 px-6 pt-16">
            {/* Logo */}
            <View className="items-center mb-8">
              <Image
                source={require('../assets/images/logo.png')}
                className="w-40 h-16"
                resizeMode="contain"
              />
            </View>

            {/* Create Account Text */}
            <View className="mb-6">
              <Text className="text-2xl font-cairo-bold text-gray-900 mb-2">
                Create Account
              </Text>
              <Text className="text-base font-cairo-regular text-gray-500">
                Create new account to join SAFQA
              </Text>
            </View>

            {/* User Type Selection */}
            <View className="flex-row mb-6 gap-4">
              {/* Car Owner */}
              <TouchableOpacity
                onPress={() => setUserType('car-owner')}
                className={`flex-1 h-24 rounded-lg items-center justify-center ${
                  userType === 'car-owner' ? 'bg-primary' : 'bg-gray-100'
                }`}
              >
                <Image
                  source={require('../assets/images/auth/car-icon.png')}
                  className="w-8 h-8 mb-2"
                  resizeMode="contain"
                />
                <Text
                  className={`font-cairo-semibold ${
                    userType === 'car-owner' ? 'text-black' : 'text-gray-600'
                  }`}
                >
                  Car Owner
                </Text>
              </TouchableOpacity>

              {/* Transporter */}
              <TouchableOpacity
                onPress={() => setUserType('transporter')}
                className={`flex-1 h-24 rounded-lg items-center justify-center ${
                  userType === 'transporter' ? 'bg-primary' : 'bg-gray-100'
                }`}
              >
                <Image
                  source={require('../assets/images/auth/truck-icon.png')}
                  className="w-8 h-8 mb-2"
                  resizeMode="contain"
                />
                <Text
                  className={`font-cairo-semibold ${
                    userType === 'transporter' ? 'text-black' : 'text-gray-600'
                  }`}
                >
                  Transporter
                </Text>
              </TouchableOpacity>
            </View>

            {/* Name Inputs */}
            <View className="flex-row mb-4 gap-4">
              <TextInput
                value={firstName}
                onChangeText={setFirstName}
                placeholder="First Name*"
                placeholderTextColor="#9CA3AF"
                className="flex-1 h-14 px-4 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                autoCapitalize="words"
              />
              <TextInput
                value={lastName}
                onChangeText={setLastName}
                placeholder="Last Name*"
                placeholderTextColor="#9CA3AF"
                className="flex-1 h-14 px-4 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                autoCapitalize="words"
              />
            </View>

            {/* Email Input */}
            <View className="mb-4">
              <TextInput
                value={email}
                onChangeText={setEmail}
                placeholder="Email ID*"
                placeholderTextColor="#9CA3AF"
                className="w-full h-14 px-4 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
            </View>

            {/* Mobile Number Input */}
            <View className="flex-row mb-4 gap-4">
              <TouchableOpacity className="flex-row items-center bg-gray-50 rounded-lg px-3 h-14 w-20">
                <Text className="font-cairo-regular text-gray-700 mr-1">🇸🇦</Text>
                <Text className="font-cairo-regular text-gray-700">+966</Text>
                <Ionicons name="chevron-down" size={16} color="#9CA3AF" className="ml-1" />
              </TouchableOpacity>
              <TextInput
                value={mobileNumber}
                onChangeText={setMobileNumber}
                placeholder="Mobile Number*"
                placeholderTextColor="#9CA3AF"
                className="flex-1 h-14 px-4 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                keyboardType="phone-pad"
              />
            </View>

            {/* Password Input */}
            <View className="mb-4 relative">
              <TextInput
                value={password}
                onChangeText={setPassword}
                placeholder="Password*"
                placeholderTextColor="#9CA3AF"
                className="w-full h-14 px-4 pr-12 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                secureTextEntry={!showPassword}
                autoComplete="new-password"
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-4"
              >
                <Ionicons
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color="#9CA3AF"
                />
              </TouchableOpacity>
            </View>

            {/* Confirm Password Input */}
            <View className="mb-6 relative">
              <TextInput
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder="Confirm Password*"
                placeholderTextColor="#9CA3AF"
                className="w-full h-14 px-4 pr-12 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                secureTextEntry={!showConfirmPassword}
                autoComplete="new-password"
              />
              <TouchableOpacity
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-4 top-4"
              >
                <Ionicons
                  name={showConfirmPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color="#9CA3AF"
                />
              </TouchableOpacity>
            </View>

            {/* Checkboxes */}
            <View className="mb-8">
              {/* Accept Terms */}
              <TouchableOpacity
                onPress={() => setAcceptTerms(!acceptTerms)}
                className="flex-row items-center mb-4"
              >
                <View
                  className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${
                    acceptTerms
                      ? 'bg-primary border-primary'
                      : 'bg-white border-gray-300'
                  }`}
                >
                  {acceptTerms && (
                    <Ionicons name="checkmark" size={12} color="black" />
                  )}
                </View>
                <Text className="font-cairo-regular text-gray-700 flex-1">
                  Accept term & condition
                </Text>
              </TouchableOpacity>

              {/* Accept Notifications */}
              <TouchableOpacity
                onPress={() => setAcceptNotifications(!acceptNotifications)}
                className="flex-row items-center"
              >
                <View
                  className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${
                    acceptNotifications
                      ? 'bg-primary border-primary'
                      : 'bg-white border-gray-300'
                  }`}
                >
                  {acceptNotifications && (
                    <Ionicons name="checkmark" size={12} color="black" />
                  )}
                </View>
                <Text className="font-cairo-regular text-gray-700 flex-1">
                  Agree to receiver notification emails
                </Text>
              </TouchableOpacity>
            </View>

            {/* Sign Up Button */}
            <TouchableOpacity
              onPress={handleSignUp}
              className="w-full h-14 bg-primary rounded-lg items-center justify-center mb-6"
            >
              <Text className="font-cairo-semibold text-lg text-black">
                Sign up
              </Text>
            </TouchableOpacity>

            {/* Sign In Link */}
            <View className="items-center mb-6">
              <Text className="font-cairo-regular text-gray-500">
                Already have account?{' '}
                <Link href="/signin" asChild>
                  <Text className="text-primary font-cairo-medium">
                    Sign in
                  </Text>
                </Link>
              </Text>
            </View>
          </View>

          {/* Bottom Indicator */}
          <View className="items-center pb-8">
            <View className="w-32 h-1 bg-black rounded-full" />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
