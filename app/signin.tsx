import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { Link, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function SignIn() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Test@1234');
  const [rememberMe, setRememberMe] = useState(true);
  const [showPassword, setShowPassword] = useState(false);

  const handleSignIn = () => {
    // Handle sign in logic here
    console.log('Sign in with:', { email, password, rememberMe });
    // Navigate to main app
    router.replace('/(tabs)');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-1 px-6 pt-16">
            {/* Logo */}
            <View className="items-center mb-12">
              <Image
                source={require('../assets/images/logo.png')}
                className="w-40 h-16"
                resizeMode="contain"
              />
            </View>

            {/* Welcome Text */}
            <View className="mb-8">
              <Text className="text-2xl font-cairo-bold text-gray-900 mb-2">
                Welcome Back
              </Text>
              <Text className="text-base font-cairo-regular text-gray-500">
                Welcome, Sign in to your account
              </Text>
            </View>

            {/* Email Input */}
            <View className="mb-4">
              <TextInput
                value={email}
                onChangeText={setEmail}
                placeholder="Email"
                placeholderTextColor="#9CA3AF"
                className="w-full h-14 px-4 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
            </View>

            {/* Password Input */}
            <View className="mb-4 relative">
              <TextInput
                value={password}
                onChangeText={setPassword}
                placeholder="Password"
                placeholderTextColor="#9CA3AF"
                className="w-full h-14 px-4 pr-12 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                secureTextEntry={!showPassword}
                autoComplete="password"
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-4"
              >
                <Ionicons
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color="#9CA3AF"
                />
              </TouchableOpacity>
            </View>

            {/* Remember Me and Forgot Password */}
            <View className="flex-row items-center justify-between mb-8">
              <TouchableOpacity
                onPress={() => setRememberMe(!rememberMe)}
                className="flex-row items-center"
              >
                <View
                  className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${
                    rememberMe
                      ? 'bg-primary border-primary'
                      : 'bg-white border-gray-300'
                  }`}
                >
                  {rememberMe && (
                    <Ionicons name="checkmark" size={12} color="white" />
                  )}
                </View>
                <Text className="font-cairo-regular text-gray-700">
                  Remember me
                </Text>
              </TouchableOpacity>

              <TouchableOpacity>
                <Text className="font-cairo-regular text-primary">
                  Forget Password?
                </Text>
              </TouchableOpacity>
            </View>

            {/* Sign In Button */}
            <TouchableOpacity
              onPress={handleSignIn}
              className="w-full h-14 bg-primary rounded-lg items-center justify-center mb-8"
            >
              <Text className="font-cairo-semibold text-lg text-black">
                Sign In
              </Text>
            </TouchableOpacity>

            {/* Divider */}
            <View className="flex-row items-center mb-8">
              <View className="flex-1 h-px bg-gray-300" />
              <Text className="mx-4 font-cairo-regular text-gray-500">
                Or sign in with
              </Text>
              <View className="flex-1 h-px bg-gray-300" />
            </View>

            {/* Terms and Conditions */}
            <View className="items-center mb-6">
              <Text className="font-cairo-regular text-gray-500 text-center">
                By Signing you agree to our{' '}
                <Text className="text-black font-cairo-medium">
                  Terms & conditions
                </Text>{' '}
                &{' '}
                <Text className="text-black font-cairo-medium">
                  Privacy policy
                </Text>
              </Text>
            </View>

            {/* Sign Up Link */}
            <View className="items-center">
              <Text className="font-cairo-regular text-gray-500">
                New to SAFQA Shipping?{' '}
                <Link href="/signup" asChild>
                  <Text className="text-primary font-cairo-medium">
                    Join SAFQA Shipping
                  </Text>
                </Link>
              </Text>
            </View>
          </View>

          {/* Bottom Indicator */}
          <View className="items-center pb-8">
            <View className="w-32 h-1 bg-black rounded-full" />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
